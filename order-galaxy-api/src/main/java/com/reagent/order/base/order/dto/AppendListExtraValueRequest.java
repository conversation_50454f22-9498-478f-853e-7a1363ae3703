package com.reagent.order.base.order.dto;

import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;

public class AppendListExtraValueRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单ID")
    private Integer orderId;

    @ModelProperty(value = "扩展键，仅支持List类型的extraKey", enumLink = "com.ruijing.store.order.api.base.orderextra.enums.OrderExtraEnum")
    private Integer extraKey;

    @ModelProperty("要追加的值列表")
    private List<String> appendValues;

    @ModelProperty("扩展键描述")
    private String extraKeyDesc;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getExtraKey() {
        return extraKey;
    }

    public void setExtraKey(Integer extraKey) {
        this.extraKey = extraKey;
    }

    public List<String> getAppendValues() {
        return appendValues;
    }

    public void setAppendValues(List<String> appendValues) {
        this.appendValues = appendValues;
    }

    public String getExtraKeyDesc() {
        return extraKeyDesc;
    }

    public void setExtraKeyDesc(String extraKeyDesc) {
        this.extraKeyDesc = extraKeyDesc;
    }

    @Override
    public String toString() {
        return "AppendListExtraValueRequest{" +
                "orderId=" + orderId +
                ", extraKey=" + extraKey +
                ", appendValues=" + appendValues +
                ", extraKeyDesc='" + extraKeyDesc + '\'' +
                '}';
    }
}
